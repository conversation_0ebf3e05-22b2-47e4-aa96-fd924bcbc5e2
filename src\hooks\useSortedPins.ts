import { useMemo, useCallback, useRef } from 'react';
import { PinData } from '@/components/PinCard';
import { filterAndSort, SortType, measureSortPerformance } from '@/utils/sortUtils';

// 缓存键类型
interface CacheKey {
  searchQuery: string;
  filterBy: string;
  sortType: SortType;
  dataHash: string;
}

// 缓存项类型
interface CacheItem {
  key: CacheKey;
  result: PinData[];
  timestamp: number;
}

// 生成数据哈希值（简单版本）
const generateDataHash = (pins: PinData[]): string => {
  return `${pins.length}_${pins[0]?.id || ''}_${pins[pins.length - 1]?.id || ''}`;
};

// 比较缓存键是否相同
const isSameCacheKey = (key1: CacheKey, key2: CacheKey): boolean => {
  return (
    key1.searchQuery === key2.searchQuery &&
    key1.filterBy === key2.filterBy &&
    key1.sortType === key2.sortType &&
    key1.dataHash === key2.dataHash
  );
};

// 排序缓存Hook
export const useSortedPins = (
  pins: PinData[],
  searchQuery: string,
  filterBy: string,
  sortType: SortType
) => {
  // 缓存引用
  const cacheRef = useRef<CacheItem[]>([]);
  const maxCacheSize = 10; // 最大缓存数量
  const cacheExpireTime = 5 * 60 * 1000; // 5分钟过期

  // 清理过期缓存
  const cleanExpiredCache = useCallback(() => {
    const now = Date.now();
    cacheRef.current = cacheRef.current.filter(
      item => now - item.timestamp < cacheExpireTime
    );
  }, []);

  // 查找缓存
  const findCache = useCallback((key: CacheKey): PinData[] | null => {
    cleanExpiredCache();
    
    const cached = cacheRef.current.find(item => isSameCacheKey(item.key, key));
    return cached ? cached.result : null;
  }, [cleanExpiredCache]);

  // 添加到缓存
  const addToCache = useCallback((key: CacheKey, result: PinData[]) => {
    // 移除最旧的缓存项
    if (cacheRef.current.length >= maxCacheSize) {
      cacheRef.current.shift();
    }
    
    cacheRef.current.push({
      key,
      result,
      timestamp: Date.now()
    });
  }, []);

  // 使用useMemo进行计算和缓存
  const sortedPins = useMemo(() => {
    const dataHash = generateDataHash(pins);
    const cacheKey: CacheKey = {
      searchQuery,
      filterBy,
      sortType,
      dataHash
    };

    // 尝试从缓存获取
    const cached = findCache(cacheKey);
    if (cached) {
      console.log('🚀 使用缓存的排序结果');
      return cached;
    }

    // 执行排序并测量性能
    const result = measureSortPerformance(
      () => filterAndSort(pins, searchQuery, filterBy, sortType),
      `排序操作 (${pins.length}条数据, 排序:${sortType}, 筛选:${filterBy})`
    );

    // 添加到缓存
    addToCache(cacheKey, result);

    return result;
  }, [pins, searchQuery, filterBy, sortType, findCache, addToCache]);

  // 获取缓存统计信息
  const getCacheStats = useCallback(() => {
    cleanExpiredCache();
    return {
      cacheSize: cacheRef.current.length,
      maxCacheSize,
      hitRate: cacheRef.current.length > 0 ? 'N/A' : '0%' // 简化版本
    };
  }, [cleanExpiredCache]);

  // 清空缓存
  const clearCache = useCallback(() => {
    cacheRef.current = [];
    console.log('🗑️ 排序缓存已清空');
  }, []);

  return {
    sortedPins,
    getCacheStats,
    clearCache
  };
};
