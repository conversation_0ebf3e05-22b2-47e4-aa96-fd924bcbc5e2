# 头像文件目录

这个目录用于存放用户的真实头像图片文件。

## 文件命名规范

- 支持的格式：`.jpg`, `.jpeg`, `.png`, `.webp`
- 建议尺寸：至少 64x64 像素，推荐 128x128 或更高
- 文件名应该简洁明了，避免特殊字符

## 配置方法

1. 将头像图片文件放置在此目录下
2. 在 `public/data/avatars.json` 中添加昵称到文件路径的映射
3. 路径格式：`/avatars/filename.ext`

## 示例配置

```json
{
  "用户昵称": "/avatars/user.jpg",
  "默认头像": "/avatars/default.jpg"
}
```

## 注意事项

- 如果配置的头像文件不存在，系统会自动回退到SVG生成的头像
- 建议为常见用户配置真实头像以提升用户体验
- 可以配置一个"默认头像"作为通用回退选项
