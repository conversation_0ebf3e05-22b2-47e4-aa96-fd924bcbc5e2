import { PinData, ContentElement } from '@/components/PinCard';
import { JsonPinData, JsonPinsData } from '@/types/jsonData';
import { generateAvatar } from '@/utils/avatarUtils';

// Unix时间戳转ISO格式
export const unixToISO = (unixTimestamp: number): string => {
  return new Date(unixTimestamp * 1000).toISOString();
};

// 检测是否为GIF文件
const isGifUrl = (url: string): boolean => {
  return url.toLowerCase().includes('.gif');
};

// 检测是否为无效的图片URL
const isInvalidImageUrl = (url: string): boolean => {
  // 过滤掉无法正常显示的图片URL
  const invalidDomains = [
    'https://multimedia.nt.qq.com.cn'
  ];

  return invalidDomains.some(domain => url.startsWith(domain));
};

// 提取内容和类型，返回null表示应该过滤掉这个消息
export const extractContent = (content: JsonPinData['content']) => {
  // 过滤并转换内容元素
  const validElements: ContentElement[] = [];
  let combinedText = '';
  let hasValidImage = false;
  let firstValidImageUrl = '';

  for (const item of content) {
    if (item.type === 'text' && item.data.text && item.data.text.trim()) {
      // 处理文本内容
      const text = item.data.text.trim();
      validElements.push({
        type: 'text',
        data: { text }
      });
      combinedText += text;
    } else if (item.type === 'image' && item.data.url) {
      // 处理图片内容
      if (!isInvalidImageUrl(item.data.url)) {
        const isGif = isGifUrl(item.data.url);
        validElements.push({
          type: isGif ? 'gif' : 'image',
          data: { url: item.data.url }
        });

        if (!hasValidImage) {
          hasValidImage = true;
          firstValidImageUrl = item.data.url;
        }
      }
    }
  }

  // 如果没有任何有效内容，返回null
  if (validElements.length === 0) {
    return null;
  }

  // 确定内容类型
  const hasText = validElements.some(el => el.type === 'text');
  const hasImage = validElements.some(el => el.type === 'image' || el.type === 'gif');

  let contentType: 'text' | 'image' | 'gif' | 'mixed';

  if (hasText && hasImage) {
    contentType = 'mixed';
  } else if (hasText) {
    contentType = 'text';
  } else {
    // 只有图片的情况，检查是否为GIF
    const firstImageElement = validElements.find(el => el.type === 'image' || el.type === 'gif');
    contentType = firstImageElement?.type === 'gif' ? 'gif' : 'image';
  }

  return {
    contentType,
    content: combinedText || '', // 用于搜索和向后兼容
    imageUrl: firstValidImageUrl || undefined, // 用于向后兼容
    contentElements: validElements
  };
};

// 单个数据转换函数，返回null表示应该过滤掉这个消息
export const transformJsonPin = (jsonPin: JsonPinData, index: number): PinData | null => {
  const contentResult = extractContent(jsonPin.content);

  // 如果内容提取返回null，说明应该过滤掉这个消息
  if (contentResult === null) {
    return null;
  }

  const { contentType, content, imageUrl, contentElements } = contentResult;

  return {
    id: `pin_${index}_${jsonPin.operator_time}`,
    content,
    contentType,
    imageUrl,
    contentElements,
    timestamp: unixToISO(jsonPin.operator_time),
    setter: {
      name: jsonPin.operator_nick,
      avatar: generateAvatar(jsonPin.operator_nick)
    },
    sender: {
      name: jsonPin.sender_nick,
      avatar: generateAvatar(jsonPin.sender_nick)
    },
    groupName: undefined // JSON数据中没有群组信息，可以后续添加
  };
};

// 批量数据转换函数，自动过滤掉无效的消息
export const transformJsonPins = (jsonPins: JsonPinsData): PinData[] => {
  return jsonPins
    .map((jsonPin, index) => transformJsonPin(jsonPin, index))
    .filter((pin): pin is PinData => pin !== null); // 过滤掉null值
};
