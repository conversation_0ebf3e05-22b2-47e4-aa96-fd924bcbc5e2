/**
 * HighlightText 组件测试
 * 测试搜索高亮功能的各种场景
 */

import { render, screen } from '@testing-library/react';
import HighlightText from '../HighlightText';

describe('HighlightText', () => {
  it('应该在没有搜索查询时显示原始文本', () => {
    render(<HighlightText text="Hello World" searchQuery="" />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  it('应该高亮单个关键词', () => {
    render(<HighlightText text="Hello World" searchQuery="Hello" />);
    const highlightedElement = screen.getByText('Hello');
    expect(highlightedElement.tagName).toBe('MARK');
  });

  it('应该高亮多个关键词', () => {
    render(<HighlightText text="Hello World Test" searchQuery="Hello Test" />);
    expect(screen.getByText('Hello').tagName).toBe('MARK');
    expect(screen.getByText('Test').tagName).toBe('MARK');
    expect(screen.getByText('World').tagName).not.toBe('MARK');
  });

  it('应该进行大小写不敏感的匹配', () => {
    render(<HighlightText text="Hello World" searchQuery="hello" />);
    const highlightedElement = screen.getByText('Hello');
    expect(highlightedElement.tagName).toBe('MARK');
  });

  it('应该处理特殊字符', () => {
    render(<HighlightText text="Hello (World)" searchQuery="(World)" />);
    const highlightedElement = screen.getByText('(World)');
    expect(highlightedElement.tagName).toBe('MARK');
  });

  it('应该处理空白字符分隔的多个关键词', () => {
    render(<HighlightText text="Hello Beautiful World" searchQuery="  Hello   World  " />);
    expect(screen.getByText('Hello').tagName).toBe('MARK');
    expect(screen.getByText('World').tagName).toBe('MARK');
    expect(screen.getByText('Beautiful').tagName).not.toBe('MARK');
  });

  it('应该应用自定义CSS类名', () => {
    render(
      <HighlightText 
        text="Hello World" 
        searchQuery="Hello" 
        className="custom-class"
        highlightClassName="custom-highlight"
      />
    );
    const container = screen.getByText('Hello').closest('span');
    expect(container).toHaveClass('custom-class');
    expect(screen.getByText('Hello')).toHaveClass('custom-highlight');
  });

  it('应该处理部分匹配', () => {
    render(<HighlightText text="Testing" searchQuery="Test" />);
    const highlightedElement = screen.getByText('Test');
    expect(highlightedElement.tagName).toBe('MARK');
  });

  it('应该处理重复的关键词', () => {
    render(<HighlightText text="Hello Hello World" searchQuery="Hello" />);
    const highlightedElements = screen.getAllByText('Hello');
    expect(highlightedElements).toHaveLength(2);
    highlightedElements.forEach(element => {
      expect(element.tagName).toBe('MARK');
    });
  });

  it('应该处理中文文本', () => {
    render(<HighlightText text="你好世界" searchQuery="你好" />);
    const highlightedElement = screen.getByText('你好');
    expect(highlightedElement.tagName).toBe('MARK');
  });
});
