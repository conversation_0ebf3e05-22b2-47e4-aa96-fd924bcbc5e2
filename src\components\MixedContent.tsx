'use client';

import { memo } from 'react';
import GifImage from './GifImage';
import LazyImage from './LazyImage';
import HighlightText from './HighlightText';
import { ContentElement } from './PinCard';

interface MixedContentProps {
  contentElements: ContentElement[];
  searchQuery?: string;
  className?: string;
}

const MixedContent = memo(function MixedContent({
  contentElements,
  searchQuery = '',
  className = ''
}: MixedContentProps) {
  
  if (!contentElements || contentElements.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {contentElements.map((element, index) => {
        const key = `${element.type}-${index}`;
        
        if (element.type === 'text' && element.data.text) {
          return (
            <div key={key} className="text-content">
              <HighlightText
                text={element.data.text}
                searchQuery={searchQuery}
                className="text-gray-900 text-sm leading-relaxed break-words block"
                highlightClassName="bg-yellow-200 text-yellow-900 px-0.5 rounded"
              />
            </div>
          );
        }
        
        if ((element.type === 'image' || element.type === 'gif') && element.data.url) {
          return (
            <div key={key} className="image-content rounded-lg overflow-hidden">
              {element.type === 'gif' ? (
                <GifImage
                  src={element.data.url}
                  alt="精华动图"
                  className="w-full"
                />
              ) : (
                <LazyImage
                  src={element.data.url}
                  alt="精华内容"
                  width={400}
                  height={300}
                  className="w-full h-auto object-cover"
                />
              )}
            </div>
          );
        }
        
        return null;
      })}
    </div>
  );
});

export default MixedContent;
