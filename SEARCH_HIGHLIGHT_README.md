# 搜索高亮功能说明

## 功能概述

为Q精华Hub添加了搜索词高亮显示功能，用户在搜索时，匹配的关键词会在搜索结果中高亮显示，提升用户体验。

## 新增文件

### 1. 核心组件
- `src/components/HighlightText.tsx` - 搜索词高亮组件
- `src/utils/highlightUtils.ts` - 搜索高亮工具函数

### 2. 测试文件
- `src/components/__tests__/HighlightText.test.tsx` - 高亮组件测试
- `src/utils/__tests__/highlightUtils.test.ts` - 工具函数测试

### 3. 演示页面
- `src/app/demo/page.tsx` - 搜索高亮功能演示页面

## 修改的文件

### 1. 组件修改
- `src/components/PinCard.tsx` - 集成搜索词高亮功能
- `src/components/MasonryGrid.tsx` - 传递搜索查询参数
- `src/app/page.tsx` - 传递搜索查询到网格组件

## 功能特性

### ✅ 核心功能
- **多关键词搜索**：支持用空格分隔的多个关键词
- **大小写不敏感**：自动处理大小写匹配
- **中英文支持**：完美支持中英文混合搜索
- **特殊字符处理**：自动转义正则表达式特殊字符
- **部分匹配**：支持关键词的部分匹配

### 🎨 样式定制
- **多种高亮样式**：默认、主要、次要三种预设样式
- **自定义样式**：支持完全自定义高亮样式
- **响应式设计**：适配不同屏幕尺寸

### ⚡ 性能优化
- **高效文本分割**：优化的正则表达式处理
- **React.memo优化**：避免不必要的重渲染
- **防抖搜索**：已有的防抖机制减少搜索频率

## 使用方法

### 基本用法

```tsx
import HighlightText from '@/components/HighlightText';

<HighlightText
  text="要高亮的文本内容"
  searchQuery="搜索关键词"
  className="自定义文本样式"
  highlightClassName="自定义高亮样式"
/>
```

### 高级用法

```tsx
import { getHighlightClassName } from '@/utils/highlightUtils';

<HighlightText
  text="Hello World 你好世界"
  searchQuery="Hello 世界"
  className="text-gray-900 leading-relaxed"
  highlightClassName={getHighlightClassName('primary')}
/>
```

## 工具函数

### 主要函数

1. **containsSearchKeywords(text, searchQuery)** - 检查文本是否包含搜索关键词
2. **extractKeywords(searchQuery)** - 提取搜索关键词数组
3. **createHighlightRegex(searchQuery)** - 创建搜索高亮正则表达式
4. **splitTextForHighlight(text, searchQuery)** - 分割文本用于高亮显示
5. **calculateMatchScore(text, searchQuery)** - 计算搜索匹配度分数

### 使用示例

```typescript
import { containsSearchKeywords, calculateMatchScore } from '@/utils/highlightUtils';

// 检查是否包含关键词
const hasKeywords = containsSearchKeywords('Hello World', 'Hello');

// 计算匹配分数
const score = calculateMatchScore('Hello World Test', 'Hello Test');
```

## 高亮样式

### 预设样式

1. **default**: `bg-yellow-200 text-yellow-900 px-0.5 rounded`
2. **primary**: `bg-blue-200 text-blue-900 px-0.5 rounded font-medium`
3. **secondary**: `bg-green-200 text-green-900 px-0.5 rounded`

### 应用位置

- **内容文本**：使用默认黄色高亮
- **发送者名称**：使用蓝色主要样式高亮
- **设置者名称**：使用蓝色主要样式高亮

## 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行特定测试
npm test HighlightText
npm test highlightUtils
```

### 测试覆盖

- ✅ 单个关键词高亮
- ✅ 多个关键词高亮
- ✅ 大小写不敏感匹配
- ✅ 特殊字符处理
- ✅ 中文文本支持
- ✅ 边界情况处理
- ✅ 性能测试

## 演示页面

访问 `/demo` 页面可以查看搜索高亮功能的完整演示，包括：

- 实时搜索高亮预览
- 多种高亮样式对比
- 各种测试用例展示
- 功能特性说明

## 性能考虑

1. **React.memo优化**：HighlightText组件使用memo避免不必要渲染
2. **正则表达式缓存**：复杂正则表达式的创建和缓存
3. **防抖搜索**：利用现有的防抖机制减少搜索频率
4. **文本分割优化**：高效的文本分割算法

## 兼容性

- ✅ 支持所有现代浏览器
- ✅ 移动端完美适配
- ✅ 无障碍访问支持
- ✅ TypeScript类型安全

## 未来扩展

可以考虑的功能扩展：

1. **搜索历史**：记录用户搜索历史
2. **搜索建议**：基于内容的搜索建议
3. **高级搜索**：支持正则表达式搜索
4. **搜索统计**：搜索结果统计和分析
5. **导出功能**：导出搜索结果
