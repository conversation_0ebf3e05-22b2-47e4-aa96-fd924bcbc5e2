import { useEffect } from 'react';
import { PinData } from '@/components/PinCard';
import { avatarCache } from '@/services/avatarCacheService';
import { getAvatarPath } from '@/services/avatarService';

// 头像预加载Hook
export const useAvatarPreload = (pins: PinData[]) => {
  useEffect(() => {
    const preloadAvatars = async () => {
      if (pins.length === 0) return;

      try {
        // 提取所有唯一的用户昵称
        const nicknames = new Set<string>();
        pins.forEach(pin => {
          nicknames.add(pin.sender.name);
          nicknames.add(pin.setter.name);
        });

        // 获取真实头像路径
        const avatarUrls: string[] = [];
        for (const nickname of nicknames) {
          try {
            const avatarPath = await getAvatarPath(nickname);
            if (avatarPath) {
              avatarUrls.push(avatarPath);
            }
          } catch (error) {
            // 忽略单个头像获取失败
            console.warn(`获取头像路径失败: ${nickname}`, error);
          }
        }

        // 预加载头像
        if (avatarUrls.length > 0) {
          avatarCache.preload(avatarUrls);
          console.log(`🚀 开始预加载 ${avatarUrls.length} 个头像`);
        }

      } catch (error) {
        console.warn('头像预加载失败:', error);
      }
    };

    preloadAvatars();
  }, [pins]);

  // 返回缓存统计信息
  const getCacheStats = () => {
    return avatarCache.getStats();
  };

  return { getCacheStats };
};
