// 日期格式化工具函数

/**
 * 格式化时间显示
 * @param timestamp ISO格式的时间戳字符串
 * @returns 格式化后的时间字符串
 */
export const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  // 相对时间显示（7天内）
  if (diffInMinutes < 1) {
    return '刚刚';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  } else if (diffInDays < 7) {
    return `${diffInDays}天前`;
  } else {
    // 超过7天显示完整日期（年/月/日）
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }
};

/**
 * 格式化完整日期（总是显示年月日）
 * @param timestamp ISO格式的时间戳字符串
 * @returns 完整的年月日格式
 */
export const formatFullDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

/**
 * 格式化日期和时间
 * @param timestamp ISO格式的时间戳字符串
 * @returns 完整的日期时间格式
 */
export const formatDateTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/**
 * 格式化相对时间（总是显示相对时间，不显示具体日期）
 * @param timestamp ISO格式的时间戳字符串
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (diffInMinutes < 1) {
    return '刚刚';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  } else if (diffInDays < 30) {
    return `${diffInDays}天前`;
  } else if (diffInMonths < 12) {
    return `${diffInMonths}个月前`;
  } else {
    return `${diffInYears}年前`;
  }
};

/**
 * 判断日期是否为今天
 * @param timestamp ISO格式的时间戳字符串
 * @returns 是否为今天
 */
export const isToday = (timestamp: string): boolean => {
  const date = new Date(timestamp);
  const today = new Date();
  
  return date.getFullYear() === today.getFullYear() &&
         date.getMonth() === today.getMonth() &&
         date.getDate() === today.getDate();
};

/**
 * 判断日期是否为昨天
 * @param timestamp ISO格式的时间戳字符串
 * @returns 是否为昨天
 */
export const isYesterday = (timestamp: string): boolean => {
  const date = new Date(timestamp);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  return date.getFullYear() === yesterday.getFullYear() &&
         date.getMonth() === yesterday.getMonth() &&
         date.getDate() === yesterday.getDate();
};

/**
 * 获取友好的日期显示
 * @param timestamp ISO格式的时间戳字符串
 * @returns 友好的日期字符串
 */
export const getFriendlyDate = (timestamp: string): string => {
  if (isToday(timestamp)) {
    return '今天';
  } else if (isYesterday(timestamp)) {
    return '昨天';
  } else {
    return formatFullDate(timestamp);
  }
};
