// 日期格式化示例和说明

import { formatTime, formatFullDate, formatDateTime, formatRelativeTime, getFriendlyDate } from './dateUtils';

// 示例时间戳（假设当前时间为 2024-01-15 12:00:00）
const examples = [
  {
    desc: '刚刚发送的消息',
    timestamp: new Date('2024-01-15T11:59:45Z').toISOString(),
    formatTime: '刚刚',
    formatFullDate: '2024/01/15',
    getFriendlyDate: '今天'
  },
  {
    desc: '30分钟前的消息',
    timestamp: new Date('2024-01-15T11:30:00Z').toISOString(),
    formatTime: '30分钟前',
    formatFullDate: '2024/01/15',
    getFriendlyDate: '今天'
  },
  {
    desc: '2小时前的消息',
    timestamp: new Date('2024-01-15T10:00:00Z').toISOString(),
    formatTime: '2小时前',
    formatFullDate: '2024/01/15',
    getFriendlyDate: '今天'
  },
  {
    desc: '昨天的消息',
    timestamp: new Date('2024-01-14T12:00:00Z').toISOString(),
    formatTime: '1天前',
    formatFullDate: '2024/01/14',
    getFriendlyDate: '昨天'
  },
  {
    desc: '3天前的消息',
    timestamp: new Date('2024-01-12T12:00:00Z').toISOString(),
    formatTime: '3天前',
    formatFullDate: '2024/01/12',
    getFriendlyDate: '2024/01/12'
  },
  {
    desc: '一周前的消息（显示完整日期）',
    timestamp: new Date('2024-01-08T12:00:00Z').toISOString(),
    formatTime: '2024/01/08',  // ✅ 现在会显示完整的年月日
    formatFullDate: '2024/01/08',
    getFriendlyDate: '2024/01/08'
  },
  {
    desc: '一个月前的消息',
    timestamp: new Date('2023-12-15T12:00:00Z').toISOString(),
    formatTime: '2023/12/15',  // ✅ 显示完整的年月日
    formatFullDate: '2023/12/15',
    getFriendlyDate: '2023/12/15'
  },
  {
    desc: '去年的消息',
    timestamp: new Date('2023-06-15T12:00:00Z').toISOString(),
    formatTime: '2023/06/15',  // ✅ 显示完整的年月日
    formatFullDate: '2023/06/15',
    getFriendlyDate: '2023/06/15'
  }
];

// 在控制台中运行这些示例来查看效果
export const runExamples = () => {
  console.log('📅 日期格式化示例:');
  console.log('==================');
  
  examples.forEach(example => {
    console.log(`\n${example.desc}:`);
    console.log(`  时间戳: ${example.timestamp}`);
    console.log(`  formatTime(): ${formatTime(example.timestamp)}`);
    console.log(`  formatFullDate(): ${formatFullDate(example.timestamp)}`);
    console.log(`  getFriendlyDate(): ${getFriendlyDate(example.timestamp)}`);
  });
};

// 主要改进说明
export const improvements = {
  before: {
    description: '修改前：超过7天只显示月日',
    example: '12/25',  // 缺少年份信息
    problems: [
      '无法区分不同年份的同一天',
      '用户可能混淆年份',
      '不符合完整日期显示的需求'
    ]
  },
  after: {
    description: '修改后：超过7天显示完整年月日',
    example: '2023/12/25',  // 包含完整年份信息
    benefits: [
      '清晰显示完整日期信息',
      '避免年份混淆',
      '符合用户期望的日期格式',
      '便于用户快速识别消息时间'
    ]
  }
};

// 使用场景说明
export const usageScenarios = {
  pinCard: {
    component: 'PinCard',
    usage: 'formatTime(pin.timestamp)',
    description: '在精华消息卡片中显示消息时间',
    behavior: {
      recent: '显示相对时间（如"2小时前"）',
      old: '显示完整日期（如"2023/12/25"）'
    }
  },
  other: {
    formatFullDate: '总是显示完整日期，不考虑时间差',
    formatDateTime: '显示完整的日期和时间',
    formatRelativeTime: '总是显示相对时间，不显示具体日期',
    getFriendlyDate: '智能显示：今天/昨天/完整日期'
  }
};
