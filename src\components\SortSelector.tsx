'use client';

import { memo } from 'react';
import Dropdown, { DropdownOption } from './Dropdown';

interface SortSelectorProps {
  value: string;
  onChange: (value: string) => void;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
}

const SortSelector = memo(function SortSelector({
  value,
  onChange,
  loading = false,
  disabled = false,
  className = ''
}: SortSelectorProps) {
  
  // 排序选项配置
  const sortOptions: DropdownOption[] = [
    {
      value: 'latest',
      label: '最新',
      icon: (
        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      value: 'oldest',
      label: '最早',
      icon: (
        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ];

  return (
    <Dropdown
      options={sortOptions}
      value={value}
      onChange={onChange}
      loading={loading}
      disabled={disabled}
      placeholder="排序方式"
      className={className}
      size="md"
      position="bottom-right"
      buttonClassName="min-w-[80px]"
      menuClassName="border-gray-200 shadow-lg"
      optionClassName="text-sm"
    />
  );
});

export default SortSelector;
