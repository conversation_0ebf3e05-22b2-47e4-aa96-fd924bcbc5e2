/**
 * 搜索高亮工具函数
 * 提供搜索词高亮相关的实用函数
 */

/**
 * 检查文本是否包含搜索关键词
 * @param text 要检查的文本
 * @param searchQuery 搜索查询
 * @returns 是否包含关键词
 */
export const containsSearchKeywords = (text: string, searchQuery: string): boolean => {
  if (!searchQuery || !searchQuery.trim()) {
    return false;
  }

  const keywords = searchQuery
    .trim()
    .split(/\s+/)
    .filter(keyword => keyword.length > 0);

  if (keywords.length === 0) {
    return false;
  }

  const lowerText = text.toLowerCase();
  return keywords.some(keyword => 
    lowerText.includes(keyword.toLowerCase())
  );
};

/**
 * 提取搜索关键词数组
 * @param searchQuery 搜索查询
 * @returns 关键词数组
 */
export const extractKeywords = (searchQuery: string): string[] => {
  if (!searchQuery || !searchQuery.trim()) {
    return [];
  }

  return searchQuery
    .trim()
    .split(/\s+/)
    .filter(keyword => keyword.length > 0);
};

/**
 * 转义正则表达式特殊字符
 * @param text 要转义的文本
 * @returns 转义后的文本
 */
export const escapeRegExp = (text: string): string => {
  return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * 创建搜索高亮的正则表达式
 * @param searchQuery 搜索查询
 * @returns 正则表达式或null
 */
export const createHighlightRegex = (searchQuery: string): RegExp | null => {
  const keywords = extractKeywords(searchQuery);
  
  if (keywords.length === 0) {
    return null;
  }

  const escapedKeywords = keywords.map(escapeRegExp);
  return new RegExp(`(${escapedKeywords.join('|')})`, 'gi');
};

/**
 * 分割文本用于高亮显示
 * @param text 原始文本
 * @param searchQuery 搜索查询
 * @returns 分割后的文本片段数组，包含是否需要高亮的信息
 */
export interface TextSegment {
  text: string;
  isHighlight: boolean;
}

export const splitTextForHighlight = (text: string, searchQuery: string): TextSegment[] => {
  const regex = createHighlightRegex(searchQuery);
  
  if (!regex) {
    return [{ text, isHighlight: false }];
  }

  const keywords = extractKeywords(searchQuery);
  const parts = text.split(regex);
  
  return parts.map(part => ({
    text: part,
    isHighlight: keywords.some(keyword => 
      part.toLowerCase() === keyword.toLowerCase()
    )
  }));
};

/**
 * 获取高亮样式类名
 * @param variant 高亮样式变体
 * @returns CSS类名
 */
export const getHighlightClassName = (variant: 'default' | 'primary' | 'secondary' = 'default'): string => {
  const variants = {
    default: 'bg-yellow-200 text-yellow-900 px-0.5 rounded',
    primary: 'bg-blue-200 text-blue-900 px-0.5 rounded font-medium',
    secondary: 'bg-green-200 text-green-900 px-0.5 rounded'
  };

  return variants[variant];
};

/**
 * 计算搜索匹配度分数
 * @param text 文本内容
 * @param searchQuery 搜索查询
 * @returns 匹配度分数 (0-1)
 */
export const calculateMatchScore = (text: string, searchQuery: string): number => {
  if (!searchQuery || !searchQuery.trim()) {
    return 0;
  }

  const keywords = extractKeywords(searchQuery);
  if (keywords.length === 0) {
    return 0;
  }

  const lowerText = text.toLowerCase();
  let matchCount = 0;
  let totalMatches = 0;

  keywords.forEach(keyword => {
    const lowerKeyword = keyword.toLowerCase();
    const matches = (lowerText.match(new RegExp(escapeRegExp(lowerKeyword), 'g')) || []).length;
    if (matches > 0) {
      matchCount++;
      totalMatches += matches;
    }
  });

  // 基础匹配度：匹配的关键词数量 / 总关键词数量
  const keywordMatchRatio = matchCount / keywords.length;
  
  // 频率加权：总匹配次数的对数加权
  const frequencyWeight = Math.log(totalMatches + 1) / Math.log(10);
  
  // 长度惩罚：较短的文本获得更高分数
  const lengthPenalty = Math.min(1, 100 / (text.length + 1));
  
  return keywordMatchRatio * 0.6 + frequencyWeight * 0.3 + lengthPenalty * 0.1;
};
