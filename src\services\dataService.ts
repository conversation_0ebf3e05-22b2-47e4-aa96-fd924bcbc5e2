import { JsonFileStructure, JsonPinsData } from '@/types/jsonData';
import { PinData } from '@/components/PinCard';
import { transformJsonPins } from '@/utils/dataTransform';

// 从public/data/pins.json加载数据
export const loadPinsFromJson = async (): Promise<PinData[]> => {
  try {
    const response = await fetch('/data/pins.json');

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const jsonFileData: JsonFileStructure = await response.json();

    // 验证数据格式
    if (!jsonFileData || typeof jsonFileData !== 'object') {
      throw new Error('JSON数据格式错误：应该是对象格式');
    }

    if (!jsonFileData.essence || !Array.isArray(jsonFileData.essence)) {
      throw new Error('JSON数据格式错误：缺少essence数组字段');
    }

    const jsonData: JsonPinsData = jsonFileData.essence;

    // 转换数据格式
    const transformedPins = transformJsonPins(jsonData);

    console.log(`成功加载 ${transformedPins.length} 条精华消息`);
    return transformedPins;

  } catch (error) {
    console.error('加载JSON数据失败:', error);
    throw error;
  }
};

// 分页加载数据（如果JSON文件很大）
export const loadPinsWithPagination = async (
  page: number = 1, 
  pageSize: number = 20
): Promise<{ pins: PinData[], hasMore: boolean, total: number }> => {
  try {
    const allPins = await loadPinsFromJson();
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    const paginatedPins = allPins.slice(startIndex, endIndex);
    const hasMore = endIndex < allPins.length;
    
    return {
      pins: paginatedPins,
      hasMore,
      total: allPins.length
    };
  } catch (error) {
    console.error('分页加载数据失败:', error);
    throw error;
  }
};
