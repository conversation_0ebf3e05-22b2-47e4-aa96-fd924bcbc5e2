'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';

export interface DropdownOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface DropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  buttonClassName?: string;
  menuClassName?: string;
  optionClassName?: string;
  size?: 'sm' | 'md' | 'lg';
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
}

export default function Dropdown({
  options,
  value,
  onChange,
  placeholder = '请选择',
  disabled = false,
  loading = false,
  className = '',
  buttonClassName = '',
  menuClassName = '',
  optionClassName = '',
  size = 'md',
  position = 'bottom-left'
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0, width: 0 });
  
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);

  // 获取当前选中的选项
  const selectedOption = options.find(option => option.value === value);

  // 尺寸样式
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  // 检测是否为移动设备
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  // 计算菜单位置
  const calculateMenuPosition = useCallback(() => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const isMobileDevice = viewportWidth < 768;

    let top = buttonRect.bottom + window.scrollY;
    let left = buttonRect.left + window.scrollX;
    let width = buttonRect.width;

    // 移动端优化：使用更宽的菜单
    if (isMobileDevice) {
      width = Math.max(buttonRect.width, 200);
      // 确保菜单不超出屏幕边界
      if (left + width > viewportWidth) {
        left = viewportWidth - width - 10;
      }
      if (left < 10) {
        left = 10;
        width = viewportWidth - 20;
      }
    } else {
      // 桌面端根据position参数调整位置
      if (position.includes('top')) {
        top = buttonRect.top + window.scrollY - 200; // 估算菜单高度
      }
      if (position.includes('right')) {
        left = buttonRect.right + window.scrollX - buttonRect.width;
      }

      // 防止菜单超出视口
      if (top + 200 > viewportHeight + window.scrollY) {
        top = buttonRect.top + window.scrollY - 200;
      }
      if (left + buttonRect.width > viewportWidth + window.scrollX) {
        left = viewportWidth + window.scrollX - buttonRect.width - 10;
      }
    }

    setMenuPosition({
      top,
      left,
      width
    });
  }, [position]);

  // 打开/关闭菜单
  const toggleMenu = useCallback(() => {
    if (disabled || loading) return;
    
    if (!isOpen) {
      calculateMenuPosition();
      setIsOpen(true);
      setFocusedIndex(-1);
    } else {
      setIsOpen(false);
      setFocusedIndex(-1);
    }
  }, [isOpen, disabled, loading, calculateMenuPosition]);

  // 选择选项
  const selectOption = useCallback((optionValue: string) => {
    const option = options.find(opt => opt.value === optionValue);
    if (option && !option.disabled) {
      onChange(optionValue);
      setIsOpen(false);
      setFocusedIndex(-1);
      buttonRef.current?.focus();
    }
  }, [options, onChange]);

  // 键盘导航
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled || loading) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (!isOpen) {
          toggleMenu();
        } else if (focusedIndex >= 0) {
          const focusedOption = options[focusedIndex];
          if (focusedOption && !focusedOption.disabled) {
            selectOption(focusedOption.value);
          }
        }
        break;
      
      case 'Escape':
        if (isOpen) {
          setIsOpen(false);
          setFocusedIndex(-1);
          buttonRef.current?.focus();
        }
        break;
      
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          toggleMenu();
        } else {
          const nextIndex = focusedIndex < options.length - 1 ? focusedIndex + 1 : 0;
          setFocusedIndex(nextIndex);
        }
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          const prevIndex = focusedIndex > 0 ? focusedIndex - 1 : options.length - 1;
          setFocusedIndex(prevIndex);
        }
        break;
    }
  }, [isOpen, focusedIndex, options, disabled, loading, toggleMenu, selectOption]);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        buttonRef.current &&
        menuRef.current &&
        !buttonRef.current.contains(event.target as Node) &&
        !menuRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // 滚动时重新计算位置
  useEffect(() => {
    if (isOpen) {
      const handleScroll = () => calculateMenuPosition();
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleScroll);
      
      return () => {
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleScroll);
      };
    }
  }, [isOpen, calculateMenuPosition]);

  // 聚焦到选中的选项
  useEffect(() => {
    if (isOpen && focusedIndex >= 0 && optionRefs.current[focusedIndex]) {
      optionRefs.current[focusedIndex]?.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      });
    }
  }, [focusedIndex, isOpen]);

  const buttonClasses = `
    relative inline-flex items-center justify-between w-full
    bg-white border border-gray-300 rounded-lg
    text-gray-900 text-left
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
    transition-all duration-200
    ${isMobile ? 'px-4 py-3 text-base' : sizeClasses[size]}
    ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 active:bg-gray-50 cursor-pointer'}
    ${isOpen ? 'ring-2 ring-blue-500 border-transparent' : ''}
    ${buttonClassName}
  `.trim();

  const menuClasses = `
    absolute z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg
    max-h-60 overflow-auto
    focus:outline-none
    ${menuClassName}
  `.trim();

  const renderMenu = () => {
    if (!isOpen) return null;

    const menu = (
      <div
        ref={menuRef}
        className={menuClasses}
        style={{
          position: 'absolute',
          top: menuPosition.top,
          left: menuPosition.left,
          width: menuPosition.width,
          zIndex: 9999
        }}
        role="listbox"
        aria-labelledby="dropdown-button"
      >
        {options.map((option, index) => (
          <div
            key={option.value}
            ref={el => optionRefs.current[index] = el}
            className={`
              flex items-center cursor-pointer transition-colors duration-150
              ${isMobile ? 'px-4 py-3 text-base' : 'px-3 py-2 text-sm'}
              ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 active:bg-gray-100'}
              ${focusedIndex === index ? 'bg-blue-50 text-blue-900' : 'text-gray-900'}
              ${value === option.value ? 'bg-blue-100 text-blue-900 font-medium' : ''}
              ${index === 0 ? 'rounded-t-lg' : ''}
              ${index === options.length - 1 ? 'rounded-b-lg' : ''}
              ${optionClassName}
            `.trim()}
            onClick={() => !option.disabled && selectOption(option.value)}
            role="option"
            aria-selected={value === option.value}
            aria-disabled={option.disabled}
          >
            {option.icon && (
              <span className="mr-2 flex-shrink-0">
                {option.icon}
              </span>
            )}
            <span className="truncate">{option.label}</span>
            {value === option.value && (
              <svg className="ml-auto h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        ))}
      </div>
    );

    // 使用 Portal 渲染到 body
    return typeof window !== 'undefined' ? createPortal(menu, document.body) : null;
  };

  return (
    <div className={`relative ${className}`}>
      <button
        ref={buttonRef}
        type="button"
        className={buttonClasses}
        onClick={toggleMenu}
        onKeyDown={handleKeyDown}
        disabled={disabled || loading}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-labelledby="dropdown-button"
        id="dropdown-button"
      >
        <span className="flex items-center truncate">
          {selectedOption?.icon && (
            <span className="mr-2 flex-shrink-0">
              {selectedOption.icon}
            </span>
          )}
          <span className="truncate">
            {selectedOption?.label || placeholder}
          </span>
        </span>
        
        <span className="ml-2 flex-shrink-0">
          {loading ? (
            <div className="w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <svg
              className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          )}
        </span>
      </button>

      {renderMenu()}
    </div>
  );
}
