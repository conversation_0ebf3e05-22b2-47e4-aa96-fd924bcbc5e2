'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import PinC<PERSON>, { PinData } from './PinCard';
import LoadingSpinner from './LoadingSpinner';

interface MasonryGridProps {
  pins: PinData[];
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
  searchQuery?: string;
}

export default function MasonryGrid({
  pins,
  onLoadMore,
  hasMore = true,
  loading = false,
  searchQuery = ''
}: MasonryGridProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [columns, setColumns] = useState(3);

  // 响应式列数计算
  const updateColumns = useCallback(() => {
    if (!containerRef.current) return;
    
    const width = containerRef.current.offsetWidth;
    if (width < 640) {
      setColumns(1); // 手机端
    } else if (width < 1024) {
      setColumns(2); // 平板端
    } else if (width < 1536) {
      setColumns(3); // 桌面端
    } else {
      setColumns(4); // 大屏幕
    }
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    updateColumns();
    
    const handleResize = () => {
      updateColumns();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateColumns]);

  // 无限滚动
  useEffect(() => {
    const handleScroll = () => {
      if (!hasMore || loading) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // 当滚动到距离底部200px时触发加载
      if (scrollTop + windowHeight >= documentHeight - 200) {
        onLoadMore?.();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, loading, onLoadMore]);

  // 将卡片分配到不同列
  const distributeCards = () => {
    const columnArrays: PinData[][] = Array.from({ length: columns }, () => []);
    
    pins.forEach((pin, index) => {
      const columnIndex = index % columns;
      columnArrays[columnIndex].push(pin);
    });
    
    return columnArrays;
  };

  const columnArrays = distributeCards();

  return (
    <div className="w-full">
      <div
        ref={containerRef}
        className="grid gap-3 auto-rows-max"
        style={{
          gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`
        }}
      >
        {columnArrays.map((columnPins, columnIndex) => (
          <div key={columnIndex} className="space-y-3">
            {columnPins.map((pin) => (
              <div key={pin.id} className="break-inside-avoid">
                <PinCard pin={pin} searchQuery={searchQuery} />
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="flex items-center space-x-3">
            <LoadingSpinner size="md" className="text-blue-500" />
            <span className="text-gray-600 text-sm">加载更多内容...</span>
          </div>
        </div>
      )}

      {/* 没有更多内容提示 */}
      {!hasMore && pins.length > 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            已经到底了，没有更多内容了 🎉
          </p>
        </div>
      )}

      {/* 空状态 */}
      {pins.length === 0 && !loading && (
        <div className="text-center py-16">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            暂无精华内容
          </h3>
          <p className="text-gray-500">
            还没有任何精华消息，快去发现一些有趣的内容吧！
          </p>
        </div>
      )}
    </div>
  );
}
