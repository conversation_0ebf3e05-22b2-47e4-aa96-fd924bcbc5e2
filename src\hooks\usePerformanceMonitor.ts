import { useEffect, useRef } from 'react';
import { requestManager } from '@/services/requestManager';

// 轻量级性能监控Hook（仅开发环境）
export const usePerformanceMonitor = () => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 只在开发环境启用
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // 减少监控频率，降低资源消耗
    intervalRef.current = setInterval(() => {
      const stats = requestManager.getStats();

      // 只在有活动且有问题时输出日志
      if (stats && (stats.queueLength > 5 || stats.currentRequests > 2)) {
        console.warn('🚨 性能警告:', {
          队列积压: stats.queueLength,
          当前请求: stats.currentRequests,
          最大并发: stats.maxConcurrent
        });
      }
    }, 10000); // 改为10秒检查一次

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // 返回获取统计信息的函数（生产环境返回null）
  const getStats = () => {
    return process.env.NODE_ENV === 'development' ? requestManager.getStats() : null;
  };

  return { getStats };
};
