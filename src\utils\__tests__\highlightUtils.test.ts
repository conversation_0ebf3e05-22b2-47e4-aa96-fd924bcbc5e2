/**
 * 搜索高亮工具函数测试
 */

import {
  containsSearchKeywords,
  extractKeywords,
  escapeRegExp,
  createHighlightRegex,
  splitTextForHighlight,
  getHighlightClassName,
  calculateMatchScore
} from '../highlightUtils';

describe('highlightUtils', () => {
  describe('containsSearchKeywords', () => {
    it('应该检测到包含的关键词', () => {
      expect(containsSearchKeywords('Hello World', 'Hello')).toBe(true);
      expect(containsSearchKeywords('Hello World', 'hello')).toBe(true);
      expect(containsSearchKeywords('Hello World', 'World')).toBe(true);
    });

    it('应该检测到不包含的关键词', () => {
      expect(containsSearchKeywords('Hello World', 'Test')).toBe(false);
      expect(containsSearchKeywords('Hello World', '')).toBe(false);
    });

    it('应该处理多个关键词', () => {
      expect(containsSearchKeywords('Hello World Test', 'Hello Test')).toBe(true);
      expect(containsSearchKeywords('Hello World', 'Hello Test')).toBe(true);
      expect(containsSearchKeywords('Hello World', 'Test Missing')).toBe(false);
    });
  });

  describe('extractKeywords', () => {
    it('应该提取单个关键词', () => {
      expect(extractKeywords('Hello')).toEqual(['Hello']);
    });

    it('应该提取多个关键词', () => {
      expect(extractKeywords('Hello World')).toEqual(['Hello', 'World']);
    });

    it('应该处理多余的空格', () => {
      expect(extractKeywords('  Hello   World  ')).toEqual(['Hello', 'World']);
    });

    it('应该处理空字符串', () => {
      expect(extractKeywords('')).toEqual([]);
      expect(extractKeywords('   ')).toEqual([]);
    });
  });

  describe('escapeRegExp', () => {
    it('应该转义正则表达式特殊字符', () => {
      expect(escapeRegExp('Hello (World)')).toBe('Hello \\(World\\)');
      expect(escapeRegExp('test.com')).toBe('test\\.com');
      expect(escapeRegExp('$100')).toBe('\\$100');
    });
  });

  describe('createHighlightRegex', () => {
    it('应该创建正确的正则表达式', () => {
      const regex = createHighlightRegex('Hello World');
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex?.test('Hello')).toBe(true);
      expect(regex?.test('World')).toBe(true);
      expect(regex?.test('Test')).toBe(false);
    });

    it('应该处理空查询', () => {
      expect(createHighlightRegex('')).toBeNull();
      expect(createHighlightRegex('   ')).toBeNull();
    });
  });

  describe('splitTextForHighlight', () => {
    it('应该正确分割文本', () => {
      const result = splitTextForHighlight('Hello World Test', 'Hello Test');
      expect(result).toEqual([
        { text: 'Hello', isHighlight: true },
        { text: ' World ', isHighlight: false },
        { text: 'Test', isHighlight: true },
        { text: '', isHighlight: false }
      ]);
    });

    it('应该处理没有匹配的情况', () => {
      const result = splitTextForHighlight('Hello World', 'Test');
      expect(result).toEqual([
        { text: 'Hello World', isHighlight: false }
      ]);
    });
  });

  describe('getHighlightClassName', () => {
    it('应该返回默认样式', () => {
      expect(getHighlightClassName()).toBe('bg-yellow-200 text-yellow-900 px-0.5 rounded');
    });

    it('应该返回主要样式', () => {
      expect(getHighlightClassName('primary')).toBe('bg-blue-200 text-blue-900 px-0.5 rounded font-medium');
    });

    it('应该返回次要样式', () => {
      expect(getHighlightClassName('secondary')).toBe('bg-green-200 text-green-900 px-0.5 rounded');
    });
  });

  describe('calculateMatchScore', () => {
    it('应该计算匹配分数', () => {
      expect(calculateMatchScore('Hello World', 'Hello')).toBeGreaterThan(0);
      expect(calculateMatchScore('Hello World', 'Test')).toBe(0);
    });

    it('应该给完全匹配更高分数', () => {
      const score1 = calculateMatchScore('Hello', 'Hello');
      const score2 = calculateMatchScore('Hello World Test', 'Hello');
      expect(score1).toBeGreaterThan(score2);
    });

    it('应该处理多个关键词', () => {
      const score1 = calculateMatchScore('Hello World', 'Hello World');
      const score2 = calculateMatchScore('Hello World', 'Hello');
      expect(score1).toBeGreaterThan(score2);
    });
  });
});
