'use client';

import { useState } from 'react';
import Dropdown, { DropdownOption } from '@/components/Dropdown';
import SortSelector from '@/components/SortSelector';
import FilterSelector from '@/components/FilterSelector';

export default function DropdownDemoPage() {
  const [basicValue, setBasicValue] = useState('');
  const [iconValue, setIconValue] = useState('react');
  const [sortValue, setSortValue] = useState('latest');
  const [filterValue, setFilterValue] = useState('all');
  const [loadingValue, setLoadingValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 基础选项
  const basicOptions: DropdownOption[] = [
    { value: 'option1', label: '选项一' },
    { value: 'option2', label: '选项二' },
    { value: 'option3', label: '选项三' },
    { value: 'option4', label: '禁用选项', disabled: true },
    { value: 'option5', label: '选项五' }
  ];

  // 带图标的选项
  const iconOptions: DropdownOption[] = [
    {
      value: 'react',
      label: 'React',
      icon: (
        <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v6l7 5 7-5V7l-7-5z" />
        </svg>
      )
    },
    {
      value: 'vue',
      label: 'Vue.js',
      icon: (
        <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v6l7 5 7-5V7l-7-5z" />
        </svg>
      )
    },
    {
      value: 'angular',
      label: 'Angular',
      icon: (
        <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v6l7 5 7-5V7l-7-5z" />
        </svg>
      )
    },
    {
      value: 'svelte',
      label: 'Svelte',
      icon: (
        <svg className="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v6l7 5 7-5V7l-7-5z" />
        </svg>
      )
    }
  ];

  const handleLoadingTest = async (value: string) => {
    setIsLoading(true);
    setLoadingValue(value);
    
    // 模拟异步操作
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          自定义下拉菜单演示
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* 基础下拉菜单 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">基础下拉菜单</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择选项
                </label>
                <Dropdown
                  options={basicOptions}
                  value={basicValue}
                  onChange={setBasicValue}
                  placeholder="请选择一个选项"
                />
              </div>
              <div className="text-sm text-gray-600">
                当前选择：{basicValue || '(无)'}
              </div>
            </div>
          </div>

          {/* 带图标的下拉菜单 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">带图标的下拉菜单</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择框架
                </label>
                <Dropdown
                  options={iconOptions}
                  value={iconValue}
                  onChange={setIconValue}
                  placeholder="选择前端框架"
                />
              </div>
              <div className="text-sm text-gray-600">
                当前选择：{iconValue}
              </div>
            </div>
          </div>

          {/* 排序选择器 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">排序选择器</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  排序方式
                </label>
                <SortSelector
                  value={sortValue}
                  onChange={setSortValue}
                />
              </div>
              <div className="text-sm text-gray-600">
                当前排序：{sortValue}
              </div>
            </div>
          </div>

          {/* 筛选器 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">内容筛选器</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  内容类型
                </label>
                <FilterSelector
                  value={filterValue}
                  onChange={setFilterValue}
                />
              </div>
              <div className="text-sm text-gray-600">
                当前筛选：{filterValue}
              </div>
            </div>
          </div>

          {/* 加载状态演示 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">加载状态演示</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  异步操作
                </label>
                <Dropdown
                  options={basicOptions}
                  value={loadingValue}
                  onChange={handleLoadingTest}
                  loading={isLoading}
                  placeholder="选择后会显示加载状态"
                />
              </div>
              <div className="text-sm text-gray-600">
                {isLoading ? '正在处理...' : `当前选择：${loadingValue || '(无)'}`}
              </div>
            </div>
          </div>

          {/* 不同尺寸演示 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">不同尺寸</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  小尺寸
                </label>
                <Dropdown
                  options={basicOptions.slice(0, 3)}
                  value=""
                  onChange={() => {}}
                  size="sm"
                  placeholder="小尺寸下拉菜单"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  中等尺寸（默认）
                </label>
                <Dropdown
                  options={basicOptions.slice(0, 3)}
                  value=""
                  onChange={() => {}}
                  size="md"
                  placeholder="中等尺寸下拉菜单"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  大尺寸
                </label>
                <Dropdown
                  options={basicOptions.slice(0, 3)}
                  value=""
                  onChange={() => {}}
                  size="lg"
                  placeholder="大尺寸下拉菜单"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 功能特性说明 */}
        <div className="bg-blue-50 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-blue-900 mb-4">功能特性</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800">
            <div>
              <h3 className="font-medium mb-2">交互功能</h3>
              <ul className="space-y-1 text-sm">
                <li>✅ 鼠标点击选择</li>
                <li>✅ 键盘导航（方向键）</li>
                <li>✅ Enter/Space 选择</li>
                <li>✅ Escape 关闭</li>
                <li>✅ 点击外部关闭</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">样式特性</h3>
              <ul className="space-y-1 text-sm">
                <li>✅ 自定义样式支持</li>
                <li>✅ 图标支持</li>
                <li>✅ 多种尺寸</li>
                <li>✅ 加载状态</li>
                <li>✅ 禁用状态</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">无障碍访问</h3>
              <ul className="space-y-1 text-sm">
                <li>✅ ARIA 属性支持</li>
                <li>✅ 键盘导航</li>
                <li>✅ 焦点管理</li>
                <li>✅ 屏幕阅读器友好</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">响应式设计</h3>
              <ul className="space-y-1 text-sm">
                <li>✅ 移动端优化</li>
                <li>✅ 触摸友好</li>
                <li>✅ 自适应定位</li>
                <li>✅ 防止溢出</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 返回主页 */}
        <div className="text-center mt-8">
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回主页
          </a>
        </div>
      </div>
    </div>
  );
}
