'use client';

import { memo } from 'react';

interface HighlightTextProps {
  text: string;
  searchQuery: string;
  className?: string;
  highlightClassName?: string;
}

/**
 * 高亮文本组件
 * 在文本中高亮显示搜索关键词，支持多个关键词和大小写不敏感匹配
 */
const HighlightText = memo(function HighlightText({
  text,
  searchQuery,
  className = '',
  highlightClassName = 'bg-yellow-200 text-yellow-900 px-0.5 rounded'
}: HighlightTextProps) {
  // 如果没有搜索查询，直接返回原文本
  if (!searchQuery || !searchQuery.trim()) {
    return <span className={className}>{text}</span>;
  }

  // 清理搜索查询，移除多余空格并分割成关键词
  const keywords = searchQuery
    .trim()
    .split(/\s+/)
    .filter(keyword => keyword.length > 0);

  if (keywords.length === 0) {
    return <span className={className}>{text}</span>;
  }

  // 创建正则表达式，匹配所有关键词（大小写不敏感）
  const escapedKeywords = keywords.map(keyword => 
    keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  );
  const regex = new RegExp(`(${escapedKeywords.join('|')})`, 'gi');

  // 分割文本并高亮匹配的部分
  const parts = text.split(regex);

  return (
    <span className={className}>
      {parts.map((part, index) => {
        // 检查当前部分是否匹配任何关键词
        const isMatch = keywords.some(keyword => 
          part.toLowerCase() === keyword.toLowerCase()
        );

        if (isMatch) {
          return (
            <mark
              key={index}
              className={highlightClassName}
            >
              {part}
            </mark>
          );
        }

        return part;
      })}
    </span>
  );
});

export default HighlightText;
