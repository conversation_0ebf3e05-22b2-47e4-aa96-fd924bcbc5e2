'use client';

import { useState } from 'react';

interface GifImageProps {
  src: string;
  alt: string;
  className?: string;
}

export default function GifImage({ src, alt, className = '' }: GifImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className={`bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center min-h-[200px] ${className}`}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          <svg className="w-12 h-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <p className="text-sm">动图加载失败</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="flex items-center space-x-2 text-gray-500">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current"></div>
            <span className="text-sm">加载动图中...</span>
          </div>
        </div>
      )}

      {/* GIF图片 */}
      <div className="relative">
        <img
          src={src}
          alt={alt}
          className={`w-full h-auto object-cover rounded-lg transition-opacity duration-200 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
        />

        {/* GIF标识 */}
        <div className="absolute top-2 right-2">
          <div className="bg-black/60 text-white text-xs px-2 py-1 rounded-md font-medium">
            GIF
          </div>
        </div>
      </div>
    </div>
  );
}
