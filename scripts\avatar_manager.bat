@echo off
chcp 65001 >nul
echo 🚀 Q精华Hub 头像管理器
echo ========================

if "%1"=="" (
    echo.
    echo 使用方法: avatar_manager.bat [命令]
    echo.
    echo 可用命令:
    echo   analyze   - 分析精华消息数据
    echo   generate  - 生成头像配置模板
    echo   validate  - 验证头像配置
    echo   stats     - 显示统计信息
    echo   check     - 检查头像文件
    echo.
    echo 示例:
    echo   avatar_manager.bat analyze
    echo   avatar_manager.bat generate
    echo.
    pause
    exit /b 1
)

cd /d "%~dp0.."
python scripts/avatar_manager.py %*

if errorlevel 1 (
    echo.
    echo ❌ 执行失败，请检查Python是否已安装
    pause
)
