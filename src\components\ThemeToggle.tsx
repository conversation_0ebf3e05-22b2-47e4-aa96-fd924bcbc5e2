'use client';

import { useThemeContext } from '@/components/ThemeProvider';
import { memo } from 'react';

const ThemeToggle = memo(function ThemeToggle() {
  const { theme, resolvedTheme, toggleTheme, isLoading } = useThemeContext();

  // 获取当前主题的图标和标签
  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        );
      case 'dark':
        return (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        );
      case 'system':
        return (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light':
        return '浅色';
      case 'dark':
        return '深色';
      case 'system':
        return '跟随系统';
      default:
        return '';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800">
        <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-sm text-gray-600 dark:text-gray-400">加载中</span>
      </div>
    );
  }

  return (
    <button
      onClick={toggleTheme}
      className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 border border-gray-200 dark:border-gray-700"
      title={`当前主题: ${getThemeLabel()}，点击切换`}
      aria-label={`切换主题，当前: ${getThemeLabel()}`}
    >
      <span className="text-gray-600 dark:text-gray-400 transition-colors duration-200">
        {getThemeIcon()}
      </span>
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">
        {getThemeLabel()}
      </span>
      
      {/* 主题状态指示器 */}
      <div className="flex items-center space-x-1">
        <div className={`w-2 h-2 rounded-full transition-colors duration-200 ${
          resolvedTheme === 'dark' 
            ? 'bg-blue-500 dark:bg-blue-400' 
            : 'bg-yellow-500 dark:bg-yellow-400'
        }`} />
      </div>
    </button>
  );
});

export default ThemeToggle;
