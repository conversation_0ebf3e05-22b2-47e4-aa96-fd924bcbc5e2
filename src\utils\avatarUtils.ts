// 生成简单的头像背景色
const avatarColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
];

// 根据名字生成头像背景色
export const getAvatarColor = (name: string): string => {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % avatarColors.length;
  return avatarColors[index];
};

// 获取名字的首字母
export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// 备用头像URL生成函数（同步版本，用于SVG生成）
export const generateSVGAvatar = (name: string): string => {
  const initials = getInitials(name);
  const backgroundColor = getAvatarColor(name);

  // 创建简洁的SVG字符串
  const svg = `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle cx="20" cy="20" r="20" fill="${backgroundColor}"/><text x="20" y="26" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">${initials}</text></svg>`;

  // 直接使用URL编码，避免btoa的Unicode问题
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
};

// 主要头像生成函数（保持同步以兼容现有代码）
export const generateAvatar = (nickname: string): string => {
  // 暂时返回SVG头像，真实头像将在组件层面异步加载
  return generateSVGAvatar(nickname);
};
