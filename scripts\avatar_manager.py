#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
头像管理脚本

用于分析精华消息数据，生成和管理头像配置文件。

使用方法：
    python scripts/avatar_manager.py [命令] [选项]

命令：
    analyze     - 分析精华消息数据，提取所有用户昵称
    generate    - 生成头像配置模板
    validate    - 验证现有头像配置
    stats       - 显示头像配置统计信息
    check       - 检查头像文件是否存在
"""

import json
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import Counter
import argparse


class AvatarManager:
    def __init__(self, project_root: str = None):
        """初始化头像管理器"""
        if project_root is None:
            # 假设脚本在 scripts/ 目录下，项目根目录在上一级
            self.project_root = Path(__file__).parent.parent
        else:
            self.project_root = Path(project_root)
        
        self.pins_file = self.project_root / "public" / "data" / "pins.json"
        self.avatars_config_file = self.project_root / "public" / "data" / "avatars.json"
        self.avatars_dir = self.project_root / "public" / "avatars"
    
    def load_pins_data(self) -> List[Dict]:
        """加载精华消息数据"""
        try:
            with open(self.pins_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, dict) and 'essence' in data:
                    return data['essence']
                elif isinstance(data, list):
                    return data
                else:
                    raise ValueError("无效的数据格式")
        except FileNotFoundError:
            print(f"❌ 精华消息文件不存在: {self.pins_file}")
            return []
        except Exception as e:
            print(f"❌ 加载精华消息数据失败: {e}")
            return []
    
    def load_avatar_config(self) -> Dict[str, str]:
        """加载头像配置"""
        try:
            with open(self.avatars_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️  头像配置文件不存在: {self.avatars_config_file}")
            return {}
        except Exception as e:
            print(f"❌ 加载头像配置失败: {e}")
            return {}
    
    def save_avatar_config(self, config: Dict[str, str]) -> bool:
        """保存头像配置"""
        try:
            # 确保目录存在
            self.avatars_config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.avatars_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 头像配置已保存到: {self.avatars_config_file}")
            return True
        except Exception as e:
            print(f"❌ 保存头像配置失败: {e}")
            return False
    
    def extract_nicknames(self) -> Set[str]:
        """从精华消息数据中提取所有唯一的用户昵称"""
        pins_data = self.load_pins_data()
        nicknames = set()
        
        for pin in pins_data:
            # 提取发送者昵称
            if 'sender_nick' in pin:
                nicknames.add(pin['sender_nick'])
            
            # 提取设置者昵称
            if 'operator_nick' in pin:
                nicknames.add(pin['operator_nick'])
        
        return nicknames
    
    def sanitize_filename(self, nickname: str) -> str:
        """将昵称转换为安全的文件名"""
        # 移除或替换特殊字符
        filename = re.sub(r'[^\w\u4e00-\u9fa5]', '_', nickname)
        # 合并多个下划线
        filename = re.sub(r'_+', '_', filename)
        # 移除首尾下划线
        filename = filename.strip('_')
        # 限制长度
        if len(filename) > 50:
            filename = filename[:50]
        # 如果为空，使用默认名称
        if not filename:
            filename = 'user'
        
        return filename.lower()
    
    def analyze_data(self) -> None:
        """分析精华消息数据"""
        print("🔍 分析精华消息数据...")
        
        pins_data = self.load_pins_data()
        if not pins_data:
            return
        
        nicknames = self.extract_nicknames()
        
        print(f"\n📊 数据统计:")
        print(f"   总消息数: {len(pins_data)}")
        print(f"   唯一用户数: {len(nicknames)}")
        
        # 统计用户活跃度
        sender_count = Counter()
        operator_count = Counter()
        
        for pin in pins_data:
            if 'sender_nick' in pin:
                sender_count[pin['sender_nick']] += 1
            if 'operator_nick' in pin:
                operator_count[pin['operator_nick']] += 1
        
        print(f"\n👥 最活跃的发送者 (前10):")
        for nickname, count in sender_count.most_common(10):
            print(f"   {nickname}: {count} 条消息")
        
        print(f"\n⭐ 最活跃的设置者 (前10):")
        for nickname, count in operator_count.most_common(10):
            print(f"   {nickname}: {count} 次设置")
        
        print(f"\n📝 所有用户昵称:")
        for nickname in sorted(nicknames):
            print(f"   {nickname}")
    
    def generate_config_template(self, overwrite: bool = False) -> None:
        """生成头像配置模板"""
        print("🛠️  生成头像配置模板...")
        
        nicknames = self.extract_nicknames()
        if not nicknames:
            print("❌ 没有找到用户昵称")
            return
        
        # 加载现有配置
        existing_config = self.load_avatar_config()
        
        # 生成新配置
        new_config = {}
        
        # 保留现有配置
        if not overwrite:
            new_config.update(existing_config)
        
        # 为新用户生成配置
        for nickname in sorted(nicknames):
            if nickname not in new_config:
                filename = self.sanitize_filename(nickname)
                new_config[nickname] = f"/avatars/{filename}.jpg"
        
        # 添加默认头像
        if "默认头像" not in new_config:
            new_config["默认头像"] = "/avatars/default.jpg"
        
        # 保存配置
        if self.save_avatar_config(new_config):
            print(f"✅ 已生成 {len(new_config)} 个头像配置项")
            
            # 显示新增的配置
            new_items = set(new_config.keys()) - set(existing_config.keys())
            if new_items:
                print(f"\n🆕 新增配置项 ({len(new_items)}):")
                for nickname in sorted(new_items):
                    print(f"   {nickname} -> {new_config[nickname]}")
    
    def validate_config(self) -> None:
        """验证头像配置"""
        print("✅ 验证头像配置...")
        
        config = self.load_avatar_config()
        if not config:
            return
        
        errors = []
        warnings = []
        
        # 检查配置格式
        for nickname, path in config.items():
            if not isinstance(nickname, str) or not nickname.strip():
                errors.append(f"昵称不能为空: '{nickname}'")
            
            if not isinstance(path, str) or not path.strip():
                errors.append(f"头像路径不能为空: '{nickname}'")
            elif not path.startswith('/avatars/'):
                warnings.append(f"建议头像路径以 '/avatars/' 开头: {nickname} -> {path}")
        
        # 检查默认头像
        if "默认头像" not in config:
            warnings.append("建议配置'默认头像'作为回退选项")
        
        # 显示结果
        print(f"\n📊 验证结果:")
        print(f"   总配置项: {len(config)}")
        print(f"   错误: {len(errors)}")
        print(f"   警告: {len(warnings)}")
        
        if errors:
            print(f"\n❌ 错误:")
            for error in errors:
                print(f"   {error}")
        
        if warnings:
            print(f"\n⚠️  警告:")
            for warning in warnings:
                print(f"   {warning}")
        
        if not errors and not warnings:
            print("✅ 配置验证通过！")
    
    def show_stats(self) -> None:
        """显示头像配置统计信息"""
        print("📊 头像配置统计...")
        
        config = self.load_avatar_config()
        if not config:
            return
        
        # 统计文件扩展名
        extensions = Counter()
        for path in config.values():
            ext = Path(path).suffix.lower()
            extensions[ext] += 1
        
        print(f"\n📈 统计信息:")
        print(f"   总配置项: {len(config)}")
        print(f"   有默认头像: {'是' if '默认头像' in config else '否'}")
        
        print(f"\n📁 文件扩展名分布:")
        for ext, count in extensions.most_common():
            print(f"   {ext or '无扩展名'}: {count} 个")
    
    def check_avatar_files(self) -> None:
        """检查头像文件是否存在"""
        print("🔍 检查头像文件...")
        
        config = self.load_avatar_config()
        if not config:
            return
        
        existing_files = []
        missing_files = []
        
        for nickname, path in config.items():
            # 转换为本地文件路径
            local_path = self.project_root / "public" / path.lstrip('/')
            
            if local_path.exists():
                existing_files.append((nickname, path))
            else:
                missing_files.append((nickname, path))
        
        print(f"\n📊 文件检查结果:")
        print(f"   存在的文件: {len(existing_files)}")
        print(f"   缺失的文件: {len(missing_files)}")
        
        if existing_files:
            print(f"\n✅ 存在的头像文件:")
            for nickname, path in existing_files:
                print(f"   {nickname} -> {path}")
        
        if missing_files:
            print(f"\n❌ 缺失的头像文件:")
            for nickname, path in missing_files:
                print(f"   {nickname} -> {path}")
            
            print(f"\n💡 建议:")
            print(f"   1. 将头像文件放入 {self.avatars_dir} 目录")
            print(f"   2. 或者更新配置文件中的路径")


def main():
    parser = argparse.ArgumentParser(description="头像管理脚本")
    parser.add_argument('command', choices=['analyze', 'generate', 'validate', 'stats', 'check'],
                       help='要执行的命令')
    parser.add_argument('--overwrite', action='store_true',
                       help='生成配置时覆盖现有配置')
    parser.add_argument('--project-root', type=str,
                       help='项目根目录路径')
    
    args = parser.parse_args()
    
    manager = AvatarManager(args.project_root)
    
    print(f"🚀 头像管理器")
    print(f"项目根目录: {manager.project_root}")
    print(f"执行命令: {args.command}")
    print("-" * 50)
    
    if args.command == 'analyze':
        manager.analyze_data()
    elif args.command == 'generate':
        manager.generate_config_template(args.overwrite)
    elif args.command == 'validate':
        manager.validate_config()
    elif args.command == 'stats':
        manager.show_stats()
    elif args.command == 'check':
        manager.check_avatar_files()


if __name__ == "__main__":
    main()
