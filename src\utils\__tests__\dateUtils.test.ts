// 日期工具函数测试

import { formatTime, formatFullDate, formatDateTime, formatRelativeTime, isToday, isYesterday, getFriendlyDate } from '../dateUtils';

// 模拟当前时间为 2024-01-15 12:00:00
const mockNow = new Date('2024-01-15T12:00:00Z');

// 重写Date.now()以便测试
const originalNow = Date.now;
beforeAll(() => {
  Date.now = jest.fn(() => mockNow.getTime());
});

afterAll(() => {
  Date.now = originalNow;
});

describe('dateUtils', () => {
  describe('formatTime', () => {
    it('应该显示"刚刚"当时间差小于1分钟', () => {
      const timestamp = new Date('2024-01-15T11:59:30Z').toISOString();
      expect(formatTime(timestamp)).toBe('刚刚');
    });

    it('应该显示分钟数当时间差小于1小时', () => {
      const timestamp = new Date('2024-01-15T11:30:00Z').toISOString();
      expect(formatTime(timestamp)).toBe('30分钟前');
    });

    it('应该显示小时数当时间差小于24小时', () => {
      const timestamp = new Date('2024-01-15T08:00:00Z').toISOString();
      expect(formatTime(timestamp)).toBe('4小时前');
    });

    it('应该显示天数当时间差小于7天', () => {
      const timestamp = new Date('2024-01-12T12:00:00Z').toISOString();
      expect(formatTime(timestamp)).toBe('3天前');
    });

    it('应该显示完整日期当时间差大于7天', () => {
      const timestamp = new Date('2024-01-01T12:00:00Z').toISOString();
      expect(formatTime(timestamp)).toBe('2024/01/01');
    });

    it('应该显示完整日期包含年份当跨年时', () => {
      const timestamp = new Date('2023-12-25T12:00:00Z').toISOString();
      expect(formatTime(timestamp)).toBe('2023/12/25');
    });
  });

  describe('formatFullDate', () => {
    it('应该总是显示完整的年月日格式', () => {
      const timestamp = new Date('2024-01-15T12:00:00Z').toISOString();
      expect(formatFullDate(timestamp)).toBe('2024/01/15');
    });

    it('应该正确格式化不同年份的日期', () => {
      const timestamp = new Date('2023-12-25T12:00:00Z').toISOString();
      expect(formatFullDate(timestamp)).toBe('2023/12/25');
    });
  });

  describe('formatDateTime', () => {
    it('应该显示完整的日期时间格式', () => {
      const timestamp = new Date('2024-01-15T12:30:45Z').toISOString();
      // 注意：这里的结果可能因时区而异，在实际测试中需要考虑时区问题
      expect(formatDateTime(timestamp)).toMatch(/2024\/01\/15/);
    });
  });

  describe('formatRelativeTime', () => {
    it('应该显示相对时间而不是具体日期', () => {
      const timestamp = new Date('2023-01-15T12:00:00Z').toISOString();
      expect(formatRelativeTime(timestamp)).toBe('1年前');
    });

    it('应该显示月份当时间差大于30天但小于12个月', () => {
      const timestamp = new Date('2023-11-15T12:00:00Z').toISOString();
      expect(formatRelativeTime(timestamp)).toBe('2个月前');
    });
  });

  describe('isToday', () => {
    it('应该正确识别今天的日期', () => {
      const timestamp = new Date('2024-01-15T08:00:00Z').toISOString();
      expect(isToday(timestamp)).toBe(true);
    });

    it('应该正确识别不是今天的日期', () => {
      const timestamp = new Date('2024-01-14T12:00:00Z').toISOString();
      expect(isToday(timestamp)).toBe(false);
    });
  });

  describe('isYesterday', () => {
    it('应该正确识别昨天的日期', () => {
      const timestamp = new Date('2024-01-14T12:00:00Z').toISOString();
      expect(isYesterday(timestamp)).toBe(true);
    });

    it('应该正确识别不是昨天的日期', () => {
      const timestamp = new Date('2024-01-13T12:00:00Z').toISOString();
      expect(isYesterday(timestamp)).toBe(false);
    });
  });

  describe('getFriendlyDate', () => {
    it('应该显示"今天"当日期为今天', () => {
      const timestamp = new Date('2024-01-15T08:00:00Z').toISOString();
      expect(getFriendlyDate(timestamp)).toBe('今天');
    });

    it('应该显示"昨天"当日期为昨天', () => {
      const timestamp = new Date('2024-01-14T12:00:00Z').toISOString();
      expect(getFriendlyDate(timestamp)).toBe('昨天');
    });

    it('应该显示完整日期当日期不是今天或昨天', () => {
      const timestamp = new Date('2024-01-13T12:00:00Z').toISOString();
      expect(getFriendlyDate(timestamp)).toBe('2024/01/13');
    });
  });
});

// 实际使用示例测试
describe('实际使用场景', () => {
  it('应该正确处理各种时间场景', () => {
    const testCases = [
      {
        desc: '刚刚发送的消息',
        timestamp: new Date('2024-01-15T11:59:45Z').toISOString(),
        expected: '刚刚'
      },
      {
        desc: '30分钟前的消息',
        timestamp: new Date('2024-01-15T11:30:00Z').toISOString(),
        expected: '30分钟前'
      },
      {
        desc: '2小时前的消息',
        timestamp: new Date('2024-01-15T10:00:00Z').toISOString(),
        expected: '2小时前'
      },
      {
        desc: '3天前的消息',
        timestamp: new Date('2024-01-12T12:00:00Z').toISOString(),
        expected: '3天前'
      },
      {
        desc: '一周前的消息（应显示完整日期）',
        timestamp: new Date('2024-01-08T12:00:00Z').toISOString(),
        expected: '2024/01/08'
      },
      {
        desc: '去年的消息（应显示完整日期包含年份）',
        timestamp: new Date('2023-12-25T12:00:00Z').toISOString(),
        expected: '2023/12/25'
      }
    ];

    testCases.forEach(({ desc, timestamp, expected }) => {
      expect(formatTime(timestamp)).toBe(expected);
    });
  });
});
