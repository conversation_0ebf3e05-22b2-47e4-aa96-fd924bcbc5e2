import { AvatarConfig } from '@/types/avatarConfig';

// 全局头像配置缓存
let avatarConfigCache: AvatarConfig | null = null;
let isLoading = false;

// 从public/data/avatars.json加载头像配置
export const loadAvatarConfig = async (): Promise<AvatarConfig> => {
  // 如果已经有缓存，直接返回
  if (avatarConfigCache !== null) {
    return avatarConfigCache;
  }

  // 如果正在加载，等待加载完成
  if (isLoading) {
    return new Promise((resolve) => {
      const checkLoading = () => {
        if (!isLoading && avatarConfigCache !== null) {
          resolve(avatarConfigCache);
        } else {
          setTimeout(checkLoading, 50);
        }
      };
      checkLoading();
    });
  }

  try {
    isLoading = true;
    const response = await fetch('/data/avatars.json');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const config: AvatarConfig = await response.json();
    
    // 验证配置格式
    if (typeof config !== 'object' || config === null) {
      throw new Error('头像配置格式错误：应该是对象格式');
    }
    
    avatarConfigCache = config;
    console.log('头像配置加载成功，包含', Object.keys(config).length, '个头像映射');
    
    return config;
    
  } catch (error) {
    console.warn('头像配置加载失败，将使用默认SVG头像:', error);
    // 返回空配置，这样会回退到SVG生成
    avatarConfigCache = {};
    return {};
  } finally {
    isLoading = false;
  }
};

// 根据昵称获取头像路径
export const getAvatarPath = async (nickname: string): Promise<string | null> => {
  try {
    const config = await loadAvatarConfig();
    
    // 直接匹配昵称
    if (config[nickname]) {
      return config[nickname];
    }
    
    // 尝试匹配默认头像
    if (config['默认头像']) {
      return config['默认头像'];
    }
    
    return null;
  } catch (error) {
    console.warn('获取头像路径失败:', error);
    return null;
  }
};

import { requestManager } from './requestManager';

// 检查头像文件是否存在（使用请求管理器）
export const checkAvatarExists = async (avatarPath: string): Promise<boolean> => {
  try {
    return await requestManager.checkFileExists(avatarPath);
  } catch (error) {
    console.warn('检查头像文件失败:', avatarPath, error);
    return false;
  }
};

// 清除头像配置缓存（用于开发调试）
export const clearAvatarCache = (): void => {
  avatarConfigCache = null;
  isLoading = false;
};
