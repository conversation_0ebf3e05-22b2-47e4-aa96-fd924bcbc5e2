/**
 * Dropdown 组件测试
 * 测试自定义下拉菜单的功能性、无障碍性和响应式设计
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Dropdown, { DropdownOption } from '../Dropdown';

const mockOptions: DropdownOption[] = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3', disabled: true },
  { value: 'option4', label: '选项4', icon: <span>🔥</span> }
];

describe('Dropdown', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('应该渲染下拉按钮', () => {
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
        placeholder="请选择选项"
      />
    );

    expect(screen.getByText('请选择选项')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('应该显示选中的选项', () => {
    render(
      <Dropdown
        options={mockOptions}
        value="option1"
        onChange={mockOnChange}
      />
    );

    expect(screen.getByText('选项1')).toBeInTheDocument();
  });

  it('应该在点击时打开菜单', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    await user.click(button);

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    mockOptions.forEach(option => {
      if (!option.disabled) {
        expect(screen.getByText(option.label)).toBeInTheDocument();
      }
    });
  });

  it('应该在选择选项时调用onChange', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    await user.click(button);

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const option = screen.getByText('选项2');
    await user.click(option);

    expect(mockOnChange).toHaveBeenCalledWith('option2');
  });

  it('应该支持键盘导航', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    button.focus();

    // 按Enter打开菜单
    await user.keyboard('{Enter}');
    
    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    // 按ArrowDown导航
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{ArrowDown}');
    
    // 按Enter选择
    await user.keyboard('{Enter}');

    expect(mockOnChange).toHaveBeenCalledWith('option2');
  });

  it('应该在按Escape时关闭菜单', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    await user.click(button);

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    await user.keyboard('{Escape}');

    await waitFor(() => {
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });
  });

  it('应该在点击外部时关闭菜单', async () => {
    const user = userEvent.setup();
    
    render(
      <div>
        <Dropdown
          options={mockOptions}
          value=""
          onChange={mockOnChange}
        />
        <div data-testid="outside">外部元素</div>
      </div>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const outside = screen.getByTestId('outside');
    await user.click(outside);

    await waitFor(() => {
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });
  });

  it('应该禁用被标记为disabled的选项', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    await user.click(button);

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const disabledOption = screen.getByText('选项3');
    await user.click(disabledOption);

    // 不应该调用onChange
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('应该在loading状态下显示加载指示器', () => {
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
        loading={true}
      />
    );

    // 查找加载动画元素
    const loadingSpinner = document.querySelector('.animate-spin');
    expect(loadingSpinner).toBeInTheDocument();
  });

  it('应该在disabled状态下禁用交互', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
        disabled={true}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();

    await user.click(button);

    // 菜单不应该打开
    expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
  });

  it('应该显示选项图标', async () => {
    const user = userEvent.setup();
    
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    await user.click(button);

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    // 检查带图标的选项
    expect(screen.getByText('🔥')).toBeInTheDocument();
  });

  it('应该应用自定义CSS类名', () => {
    render(
      <Dropdown
        options={mockOptions}
        value=""
        onChange={mockOnChange}
        className="custom-dropdown"
        buttonClassName="custom-button"
      />
    );

    const container = document.querySelector('.custom-dropdown');
    expect(container).toBeInTheDocument();

    const button = document.querySelector('.custom-button');
    expect(button).toBeInTheDocument();
  });
});
