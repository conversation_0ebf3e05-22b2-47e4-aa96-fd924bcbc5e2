// JSON数据的原始结构
export interface JsonPinContent {
  type: 'text' | 'image';
  data: {
    text?: string;
    url?: string;
    summary?: string;
    file?: string;
    sub_type?: number;
    file_size?: string;
  };
}

export interface JsonPinData {
  operator_nick: string;
  sender_nick: string;
  operator_time: number; // Unix时间戳
  content: JsonPinContent[];
}

// JSON文件的根结构
export interface JsonFileStructure {
  essence: JsonPinData[];
}

export type JsonPinsData = JsonPinData[];
