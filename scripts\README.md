# 头像管理脚本使用指南

这个Python脚本可以帮助您自动管理Q精华Hub的用户头像配置。

## 快速开始

### 1. 安装Python（如果还没有）
确保您的系统已安装Python 3.6或更高版本。

### 2. 运行脚本
在项目根目录下打开终端，运行以下命令：

```bash
# 分析精华消息数据，查看所有用户
python scripts/avatar_manager.py analyze

# 生成头像配置模板
python scripts/avatar_manager.py generate

# 验证现有配置
python scripts/avatar_manager.py validate

# 查看配置统计
python scripts/avatar_manager.py stats

# 检查头像文件是否存在
python scripts/avatar_manager.py check
```

## 详细使用流程

### 步骤1：分析数据
```bash
python scripts/avatar_manager.py analyze
```
这会显示：
- 总消息数和用户数
- 最活跃的发送者和设置者
- 所有用户昵称列表

### 步骤2：生成配置模板
```bash
python scripts/avatar_manager.py generate
```
这会：
- 为所有用户生成头像配置项
- 保存到 `public/data/avatars.json`
- 显示新增的配置项

### 步骤3：准备头像文件
1. 在 `public/avatars/` 目录下放置头像图片
2. 文件名建议与配置中的路径对应
3. 支持 `.jpg`, `.png`, `.webp` 等格式

### 步骤4：检查文件
```bash
python scripts/avatar_manager.py check
```
这会显示：
- 哪些头像文件已存在
- 哪些头像文件缺失
- 给出改进建议

### 步骤5：验证配置
```bash
python scripts/avatar_manager.py validate
```
这会检查配置文件的格式和完整性。

## 命令详解

### `analyze` - 数据分析
- 分析精华消息数据
- 提取所有用户昵称
- 显示用户活跃度统计

### `generate` - 生成配置
- 为所有用户生成头像配置
- 自动生成安全的文件名
- 保留现有配置（除非使用 `--overwrite`）

### `validate` - 验证配置
- 检查配置文件格式
- 验证路径有效性
- 提供改进建议

### `stats` - 统计信息
- 显示配置项数量
- 文件扩展名分布
- 配置完整性

### `check` - 文件检查
- 检查头像文件是否存在
- 列出缺失的文件
- 提供解决方案

## 高级选项

### 覆盖现有配置
```bash
python scripts/avatar_manager.py generate --overwrite
```

### 指定项目根目录
```bash
python scripts/avatar_manager.py analyze --project-root /path/to/project
```

## 示例工作流程

```bash
# 1. 分析数据，了解有哪些用户
python scripts/avatar_manager.py analyze

# 2. 生成配置模板
python scripts/avatar_manager.py generate

# 3. 手动添加头像文件到 public/avatars/ 目录

# 4. 检查哪些文件还缺失
python scripts/avatar_manager.py check

# 5. 验证最终配置
python scripts/avatar_manager.py validate
```

## 配置文件格式

生成的 `public/data/avatars.json` 格式如下：

```json
{
  "Andrea（安之若）": "/avatars/andrea.jpg",
  "丝之鸽我来了😋": "/avatars/sizige.jpg",
  "星舟": "/avatars/xingzhou.jpg",
  "默认头像": "/avatars/default.jpg"
}
```

## 注意事项

1. **文件命名**：脚本会自动将特殊字符转换为下划线
2. **文件格式**：建议使用 `.jpg` 或 `.png` 格式
3. **文件大小**：建议头像尺寸至少 64x64 像素
4. **默认头像**：建议配置一个默认头像作为回退选项
5. **备份**：修改配置前建议备份现有文件

## 故障排除

### 脚本无法运行
- 确保在项目根目录运行
- 检查Python版本（需要3.6+）
- 确保 `public/data/pins.json` 文件存在

### 配置生成失败
- 检查文件权限
- 确保 `public/data/` 目录存在
- 检查磁盘空间

### 头像不显示
- 运行 `check` 命令检查文件
- 验证文件路径是否正确
- 确保文件格式受支持
