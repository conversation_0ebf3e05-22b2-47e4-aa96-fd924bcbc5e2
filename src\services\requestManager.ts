// 请求管理器 - 解决并发请求和资源耗尽问题

interface RequestItem {
  url: string;
  resolve: (result: boolean) => void;
  reject: (error: Error) => void;
  retryCount: number;
}

class RequestManager {
  private queue: RequestItem[] = [];
  private processing = new Set<string>(); // 正在处理的URL
  private cache = new Map<string, { result: boolean; timestamp: number }>(); // 结果缓存
  private maxConcurrent = 3; // 最大并发数
  private currentRequests = 0;
  private cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
  private maxRetries = 2;

  // 检查文件是否存在（带缓存和队列管理）
  async checkFileExists(url: string): Promise<boolean> {
    // 1. 检查缓存
    const cached = this.cache.get(url);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.result;
    }

    // 2. 检查是否正在处理
    if (this.processing.has(url)) {
      // 等待正在进行的请求完成
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          const cachedResult = this.cache.get(url);
          if (cachedResult && Date.now() - cachedResult.timestamp < this.cacheExpiry) {
            clearInterval(checkInterval);
            resolve(cachedResult.result);
          }
          if (!this.processing.has(url)) {
            clearInterval(checkInterval);
            // 如果处理完成但没有缓存结果，说明失败了
            reject(new Error('Request failed'));
          }
        }, 50);

        // 超时处理
        setTimeout(() => {
          clearInterval(checkInterval);
          reject(new Error('Request timeout'));
        }, 10000);
      });
    }

    // 3. 添加到队列
    return new Promise((resolve, reject) => {
      this.queue.push({
        url,
        resolve,
        reject,
        retryCount: 0
      });
      this.processQueue();
    });
  }

  // 处理请求队列
  private async processQueue(): Promise<void> {
    while (this.queue.length > 0 && this.currentRequests < this.maxConcurrent) {
      const item = this.queue.shift();
      if (!item) continue;

      this.currentRequests++;
      this.processing.add(item.url);

      try {
        const result = await this.performRequest(item.url);
        
        // 缓存结果
        this.cache.set(item.url, {
          result,
          timestamp: Date.now()
        });

        item.resolve(result);
      } catch (error) {
        // 重试逻辑
        if (item.retryCount < this.maxRetries) {
          item.retryCount++;
          // 延迟重试
          setTimeout(() => {
            this.queue.unshift(item); // 重新加入队列头部
            this.processQueue();
          }, 1000 * Math.pow(2, item.retryCount)); // 指数退避
        } else {
          // 缓存失败结果，避免重复请求
          this.cache.set(item.url, {
            result: false,
            timestamp: Date.now()
          });
          item.reject(error as Error);
        }
      } finally {
        this.currentRequests--;
        this.processing.delete(item.url);
        // 继续处理队列
        this.processQueue();
      }
    }
  }

  // 执行实际的网络请求
  private async performRequest(url: string): Promise<boolean> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    try {
      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'force-cache' // 强制使用缓存
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        // 特殊处理资源不足错误
        if (error.message.includes('ERR_INSUFFICIENT_RESOURCES')) {
          // 降低并发数
          this.maxConcurrent = Math.max(1, this.maxConcurrent - 1);
          console.warn(`降低并发数到 ${this.maxConcurrent} 以避免资源耗尽`);
        }
      }
      
      throw error;
    }
  }

  // 清理过期缓存
  private cleanupCache(): void {
    const now = Date.now();
    for (const [url, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.cacheExpiry) {
        this.cache.delete(url);
      }
    }
  }

  // 获取统计信息（仅开发环境）
  getStats() {
    if (process.env.NODE_ENV === 'development') {
      return {
        queueLength: this.queue.length,
        processing: this.processing.size,
        cacheSize: this.cache.size,
        currentRequests: this.currentRequests,
        maxConcurrent: this.maxConcurrent
      };
    }
    return null;
  }

  // 清空缓存和队列
  clear(): void {
    this.queue = [];
    this.processing.clear();
    this.cache.clear();
    this.currentRequests = 0;
  }
}

// 全局请求管理器实例
export const requestManager = new RequestManager();

// 定期清理缓存
setInterval(() => {
  requestManager['cleanupCache']();
}, 60000); // 每分钟清理一次
