import { PinData } from '@/components/PinCard';

// 排序类型定义
export type SortType = 'latest' | 'oldest';

// 预计算的排序键值接口
interface PinWithSortKeys extends PinData {
  _sortKeys: {
    timestamp: number;
  };
}

// 为数据添加预计算的排序键值
export const addSortKeys = (pins: PinData[]): PinWithSortKeys[] => {
  return pins.map(pin => ({
    ...pin,
    _sortKeys: {
      timestamp: new Date(pin.timestamp).getTime()
    }
  }));
};

// 高性能排序函数
export const sortPins = (pins: PinWithSortKeys[], sortType: SortType): PinWithSortKeys[] => {
  // 使用原地排序避免创建新数组
  const sortedPins = [...pins];
  
  switch (sortType) {
    case 'latest':
      sortedPins.sort((a, b) => b._sortKeys.timestamp - a._sortKeys.timestamp);
      break;
    case 'oldest':
      sortedPins.sort((a, b) => a._sortKeys.timestamp - b._sortKeys.timestamp);
      break;
    default:
      sortedPins.sort((a, b) => b._sortKeys.timestamp - a._sortKeys.timestamp);
  }
  
  return sortedPins;
};

// 移除排序键值，返回原始数据
export const removeSortKeys = (pins: PinWithSortKeys[]): PinData[] => {
  return pins.map(({ _sortKeys, ...pin }) => pin);
};

// 组合排序函数
export const performSort = (pins: PinData[], sortType: SortType): PinData[] => {
  const pinsWithKeys = addSortKeys(pins);
  const sorted = sortPins(pinsWithKeys, sortType);
  return removeSortKeys(sorted);
};

// 批量处理：筛选 + 排序的优化版本
export const filterAndSort = (
  pins: PinData[],
  searchQuery: string,
  filterBy: string,
  sortType: SortType
): PinData[] => {
  // 预计算排序键值
  let pinsWithKeys = addSortKeys(pins);
  
  // 搜索筛选
  if (searchQuery) {
    const query = searchQuery.toLowerCase();
    pinsWithKeys = pinsWithKeys.filter(pin =>
      pin.content.toLowerCase().includes(query) ||
      pin.sender.name.toLowerCase().includes(query) ||
      pin.setter.name.toLowerCase().includes(query)
    );
  }
  
  // 类型筛选
  if (filterBy !== 'all') {
    pinsWithKeys = pinsWithKeys.filter(pin => {
      // 对于混合内容，检查是否包含指定类型
      if (pin.contentType === 'mixed') {
        return pin.contentElements.some(element => {
          if (filterBy === 'text') return element.type === 'text';
          if (filterBy === 'image') return element.type === 'image';
          if (filterBy === 'gif') return element.type === 'gif';
          return false;
        });
      }
      // 对于单一类型内容，直接比较
      return pin.contentType === filterBy;
    });
  }
  
  // 排序
  const sorted = sortPins(pinsWithKeys, sortType);
  
  // 移除排序键值
  return removeSortKeys(sorted);
};

// 性能监控工具
export const measureSortPerformance = <T>(
  operation: () => T,
  operationName: string
): T => {
  const start = performance.now();
  const result = operation();
  const end = performance.now();
  
  console.log(`${operationName} 耗时: ${(end - start).toFixed(2)}ms`);
  return result;
};
